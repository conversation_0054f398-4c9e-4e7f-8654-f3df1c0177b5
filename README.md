# THJ 自动化控制系统 - 数据查询页面

## 项目概述

这是一个基于 Vue 3 + Element Plus 的数据查询系统，主要用于查询和导出各类设备记录数据。

## 功能特性

### 1. 多标签页数据查询

- **操作记录**：设备操作相关记录
- **故障记录**：设备故障信息记录
- **水深记录**：水位深度监测记录
- **运行记录**：设备运行时长记录

### 2. 灵活的筛选功能

- **设备名称**：支持模糊搜索
- **设备编码**：下拉选择
- **操作员**：下拉选择（水深记录和运行记录不显示此项）
- **时间范围**：支持日期时间区间选择

### 3. 数据表格展示

- **分页显示**：支持每页 10/20/50/100 条记录
- **响应式表格**：自适应不同屏幕尺寸
- **数据加载状态**：显示加载动画

### 4. Excel 导出功能

- 支持将当前标签页的数据导出为 Excel 文件
- 自动生成文件名（包含记录类型和日期）
- 保持表格格式和列宽

## 表格字段说明

### 操作记录

- 序号、设备编码、设备名称、控制状态、动作、操作中心、操作员、操作时间

### 故障记录

- 序号、设备编码、设备名称、故障信息、操作中心、操作员、操作时间

### 水深记录

- 序号、设备编码、设备名称、水深、更新时间

### 运行记录

- 序号、设备编码、设备名称、开始时间、关闭时间、开始时长/运行时长、操作时间

## 最近更新

### 2025-09-03 - 修复数据更新问题

**问题描述：**
操作记录页面在筛选查询时，即使接口返回正确的空数据响应，页面仍显示之前的数据，没有正确更新。

**问题原因：**
接口返回的数据结构中，当查询结果为空时，`operateList` 字段为 `null` 而不是空数组 `[]`。原代码的条件判断 `if (response && response.operateList)` 在 `operateList` 为 `null` 时会失败，导致数据不会被更新。

**修复方案：**

1. 修改所有数据加载方法的条件判断逻辑
2. 使用 `|| []` 操作符处理 `null` 值，提供默认空数组
3. 确保在任何情况下都会正确更新表格数据和分页信息

**修复文件：**

- `src/views/DataQuery.vue` - 修复了 `loadOperationData`、`loadFaultData`、`loadWaterData`、`loadPumpData` 四个方法

**测试方法：**

1. 设置筛选条件使查询结果为空
2. 点击查询按钮
3. 验证页面是否正确显示空表格和"暂无数据"提示

## 技术栈

- **前端框架**：Vue 3 (Composition API)
- **UI 组件库**：Element Plus
- **构建工具**：Vite
- **Excel 导出**：xlsx
- **路由管理**：Vue Router
- **状态管理**：Pinia

## 项目结构

```
src/
├── views/
│   └── DataQuery.vue      # 数据查询主页面
├── router/
│   └── index.js           # 路由配置
├── stores/
│   └── counter.js         # 状态管理
├── App.vue                # 根组件
└── main.js                # 应用入口
```

## 开发说明

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## Mock 数据

目前使用 Mock 数据进行测试，每个标签页包含 2 条示例记录。在实际部署时，需要：

1. 替换 Mock 数据为真实 API 调用
2. 实现筛选条件的后端查询逻辑
3. 添加错误处理和用户反馈机制

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 更新日志

### v1.0.0 (2024-01-15)

- ✅ 完成基础页面布局和功能
- ✅ 实现四个标签页的数据展示
- ✅ 添加筛选和查询功能
- ✅ 集成 Excel 导出功能
- ✅ 使用 Mock 数据进行测试
