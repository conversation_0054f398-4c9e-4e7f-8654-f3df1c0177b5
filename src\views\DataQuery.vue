<template>
  <div class="data-query-container">
    <!-- Tab栏 -->
    <div class="tabs-container">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="操作记录" name="operation"></el-tab-pane>
        <el-tab-pane label="故障记录" name="fault"></el-tab-pane>
        <el-tab-pane label="水深记录" name="depth"></el-tab-pane>
        <el-tab-pane label="运行记录" name="running"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 筛选行 -->
    <div class="filter-container">
      <div class="filter-row">
        <div class="filter-left">
          <!-- 设备名称（暂时用站点名称的字段） -->
          <div class="filter-item">
            <span class="filter-label">设备名称：</span>
            <el-input
              v-model="filters.siteName"
              placeholder="请输入设备名称"
              style="width: 200px"
              clearable
            />
          </div>

          <!-- 设备编码 -->
          <div class="filter-item">
            <span class="filter-label">设备编码：</span>
            <el-select
              v-model="filters.deviceCode"
              placeholder="请选择设备编码"
              style="width: 250px"
              clearable
              filterable
            >
              <el-option
                v-for="item in deviceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 操作员（水深记录和运行记录没有该筛选项） -->
          <div v-if="activeTab !== 'depth' && activeTab !== 'running'" class="filter-item">
            <span class="filter-label">操作员：</span>
            <el-select
              v-model="filters.operator"
              placeholder="请选择操作员"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="item in operatorOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 选择时间 -->
          <div class="filter-item">
            <span class="filter-label">选择时间：</span>
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 300px"
            />
          </div>
        </div>

        <div class="filter-right">
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="table-wrapper">
        <el-table :data="currentTableData" border v-loading="loading" :height="tableHeight">
          <!-- 序号列 -->
          <el-table-column
            type="index"
            label="序号"
            width="80"
            :index="(index) => (currentPage - 1) * pageSize + index + 1"
          />

          <!-- 动态列 -->
          <el-table-column
            v-for="column in currentColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            show-overflow-tooltip
          />
        </el-table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import * as XLSX from 'xlsx'
import { ElMessage } from 'element-plus'
import { deviceAPI, faultAPI, operateAPI, pumpAPI, waterAPI } from '@/api/index.js'

// 响应式数据
const activeTab = ref('operation')
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableHeight = ref(400)

// 筛选条件
const filters = ref({
  siteName: '', // 站点名称（对应设备名称）
  deviceCode: '',
  operator: '',
  dateRange: [],
})

// 下拉选项数据
const deviceOptions = ref([]) // 从接口获取设备列表
const operatorOptions = ref([
  { label: '系统管理员', value: '1' },
  { label: '操作员', value: '2' },
  { label: '运维人员', value: '3' },
])

// 表格数据
const tableData = ref({
  operation: [],
  fault: [],
  depth: [],
  running: [],
})

// 操作中心映射
const centerMap = {
  1: '总控中心',
  2: '东干渠管理所',
  3: '高桥管理所',
  4: '水口山管理所',
  5: '鸬鹚渡管理所',
  6: '大坝',
}

// 操作员映射
const operatorMap = {
  1: '系统管理员',
  2: '操作员',
  3: '运维人员',
}

// 控制状态映射
const statusMap = {
  0: '停止',
  1: '运行',
}

// 动作映射
const actionMap = {
  0: '关闭',
  1: '开启',
}

// 表格列配置
const columnConfigs = {
  operation: [
    { prop: 'h_devcode', label: '设备编码' },
    { prop: 'h_devname', label: '设备名称' },
    { prop: 'h_constatus', label: '控制状态' },
    { prop: 'h_action', label: '动作' },
    { prop: 'h_center', label: '操作中心' },
    { prop: 'h_operator', label: '操作员' },
    { prop: 'h_tM', label: '操作时间' },
  ],
  fault: [
    { prop: 'h_devcode', label: '设备编码' },
    { prop: 'h_devname', label: '设备名称' },
    { prop: 'h_fault', label: '故障信息' },
    { prop: 'h_center', label: '操作中心' },
    { prop: 'h_operator', label: '操作员' },
    { prop: 'h_tm', label: '操作时间' },
  ],
  depth: [
    { prop: 'h_devcode', label: '设备编码' },
    { prop: 'h_devname', label: '设备名称' },
    { prop: 'h_ss', label: '水深' },
    { prop: 'H_TM', label: '更新时间' },
  ],
  running: [
    { prop: 'h_devCode', label: '设备编码' },
    { prop: 'h_devname', label: '设备名称' },
    { prop: 'startTime', label: '开始时间' },
    { prop: 'endTime', label: '关闭时间' },
    { prop: 'duration', label: '开始时长/运行时长' },
    { prop: 'H_UpdateTime', label: '操作时间' },
  ],
}

// 计算属性
const currentColumns = computed(() => {
  return columnConfigs[activeTab.value] || []
})

const currentTableData = computed(() => {
  return tableData.value[activeTab.value] || []
})

// API 调用方法
const loadDeviceList = async () => {
  try {
    const response = await deviceAPI.getDeviceList()
    if (response && response.deviceList) {
      deviceOptions.value = response.deviceList.map((item) => ({
        label: `${item.h_devname} (${item.h_devcode})`,
        value: item.h_devcode,
        siteName: item.h_sitename,
        deviceName: item.h_devname,
      }))
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败')
  }
}

const loadOperationData = async () => {
  try {
    loading.value = true
    const params = {
      siteName: filters.value.siteName,
      deviceCode: filters.value.deviceCode,
      operator: filters.value.operator,
      startTime: filters.value.dateRange?.[0]?.split(' ')[0],
      endTime: filters.value.dateRange?.[1]?.split(' ')[0],
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    }

    const response = await operateAPI.getOperateList(params)
    if (response) {
      // 处理数据映射，operateList 可能为 null，需要处理
      const operateList = response.operateList || []
      tableData.value.operation = operateList.map((item) => ({
        ...item,
        h_center: centerMap[item.h_center] || item.h_center,
        h_operator: operatorMap[item.h_operator] || item.h_operator,
        h_constatus: statusMap[item.h_constatus] || item.h_constatus,
        h_action: actionMap[item.h_action] || item.h_action,
      }))
      total.value = response.total || 0
    } else {
      // 如果没有响应数据，清空表格
      tableData.value.operation = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取操作记录失败:', error)
    ElMessage.error('获取操作记录失败')
    // 清空数据
    tableData.value.operation = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const loadFaultData = async () => {
  try {
    loading.value = true
    const params = {
      siteName: filters.value.siteName,
      deviceCode: filters.value.deviceCode,
      operator: filters.value.operator,
      startTime: filters.value.dateRange?.[0]?.split(' ')[0],
      endTime: filters.value.dateRange?.[1]?.split(' ')[0],
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    }

    const response = await faultAPI.getFaultList(params)
    if (response) {
      // 处理数据映射，faultList 可能为 null，需要处理
      const faultList = response.faultList || []
      tableData.value.fault = faultList.map((item) => ({
        ...item,
        h_center: centerMap[item.h_center] || item.h_center,
        h_operator: operatorMap[item.h_operator] || item.h_operator,
      }))
      total.value = response.total || 0
    } else {
      // 如果没有响应数据，清空表格
      tableData.value.fault = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取故障记录失败:', error)
    ElMessage.error('获取故障记录失败')
    // 清空数据
    tableData.value.fault = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const loadWaterData = async () => {
  try {
    loading.value = true
    const params = {
      siteName: filters.value.siteName,
      deviceCode: filters.value.deviceCode,
      startTime: filters.value.dateRange?.[0]?.split(' ')[0],
      endTime: filters.value.dateRange?.[1]?.split(' ')[0],
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    }

    const response = await waterAPI.getWaterList(params)
    if (response) {
      // 处理数据，waterList 可能为 null，需要处理
      const waterList = response.waterList || []
      tableData.value.depth = waterList
      total.value = response.total || 0
    } else {
      // 如果没有响应数据，清空表格
      tableData.value.depth = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取水深记录失败:', error)
    ElMessage.error('获取水深记录失败')
    // 清空数据
    tableData.value.depth = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const loadPumpData = async () => {
  try {
    loading.value = true
    const params = {
      deviceCode: filters.value.deviceCode,
      startTime: filters.value.dateRange?.[0]?.split(' ')[0],
      endTime: filters.value.dateRange?.[1]?.split(' ')[0],
    }

    const response = await pumpAPI.getPumpList(params)
    if (response) {
      // 处理运行时长数据，pumpList 可能为 null，需要处理
      const pumpList = response.pumpList || []
      tableData.value.running = pumpList.map((item) => ({
        ...item,
        startTime: '-', // 接口没有提供开始时间
        endTime: '-', // 接口没有提供结束时间
        duration: `${item.h_day}天${item.h_hour}小时${item.h_minute}分${item.h_second}秒`,
      }))
      total.value = pumpList.length || 0
    } else {
      // 如果没有响应数据，清空表格
      tableData.value.running = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取运行记录失败:', error)
    ElMessage.error('获取运行记录失败')
    // 清空数据
    tableData.value.running = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 根据当前标签页加载对应数据
const loadCurrentTabData = () => {
  switch (activeTab.value) {
    case 'operation':
      loadOperationData()
      break
    case 'fault':
      loadFaultData()
      break
    case 'depth':
      loadWaterData()
      break
    case 'running':
      loadPumpData()
      break
  }
}

// 方法
const handleTabChange = (tabName) => {
  activeTab.value = tabName
  currentPage.value = 1

  // 切换标签页时清空筛选条件
  filters.value = {
    siteName: '',
    deviceCode: '',
    operator: '',
    dateRange: [],
  }

  loadCurrentTabData()
}

const handleReset = () => {
  filters.value = {
    siteName: '',
    deviceCode: '',
    operator: '',
    dateRange: [],
  }
}

const handleQuery = () => {
  currentPage.value = 1
  loadCurrentTabData()
}

const handleExport = () => {
  const data = currentTableData.value
  const columns = currentColumns.value

  // 创建工作表数据
  const wsData = []

  // 添加表头
  const headers = ['序号', ...columns.map((col) => col.label)]
  wsData.push(headers)

  // 添加数据行
  data.forEach((row, index) => {
    const rowData = [index + 1, ...columns.map((col) => row[col.prop] || '')]
    wsData.push(rowData)
  })

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.aoa_to_sheet(wsData)

  // 设置列宽
  const colWidths = [{ wch: 8 }, ...columns.map(() => ({ wch: 20 }))]
  ws['!cols'] = colWidths

  // 添加工作表到工作簿
  const tabNames = {
    operation: '操作记录',
    fault: '故障记录',
    depth: '水深记录',
    running: '运行记录',
  }

  XLSX.utils.book_append_sheet(wb, ws, tabNames[activeTab.value])

  // 导出文件
  const fileName = `${tabNames[activeTab.value]}_${new Date().toISOString().slice(0, 10)}.xlsx`
  XLSX.writeFile(wb, fileName)
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadCurrentTabData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadCurrentTabData()
}

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    // 计算可用高度：视窗高度 - 其他元素高度
    const windowHeight = window.innerHeight
    const tabsHeight = 60 // tabs 高度
    const filterHeight = 100 // 筛选区域高度
    const paginationHeight = 60 // 分页高度
    const padding = 40 // 容器内边距

    tableHeight.value = windowHeight - tabsHeight - filterHeight - paginationHeight - padding
  })
}

// 生命周期
onMounted(async () => {
  calculateTableHeight()

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight)

  // 加载设备列表
  await loadDeviceList()

  // 加载默认标签页数据
  loadCurrentTabData()
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})
</script>

<style scoped>
.data-query-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
}

.tabs-container {
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 4px;
  padding: 0 20px;
}

.filter-container {
  flex-shrink: 0;
  margin-top: 20px;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.filter-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  font-weight: 500;
}

.filter-right {
  display: flex;
  gap: 12px;
}

.table-container {
  flex: 1;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
}

.pagination-container {
  flex-shrink: 0;
  /* margin-top: 20px; */
  background-color: #fff;
  border-radius: 4px;
  padding: 15px 20px;
  display: flex;
  justify-content: center;
}

/* 覆盖 Element Plus 表格样式，确保表头占满宽度 */
:deep(.el-table) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.el-table__header-wrapper) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100% !important;
}

/* 确保表格列平均分布 */
:deep(.el-table .el-table__cell) {
  text-align: center;
}

:deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-tabs__nav-wrap:after) {
  background-color: none !important;
  display: none !important;
}
:deep(.el-tabs__header) {
  margin-bottom: 10px;
}
</style>
